import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ZaloService } from './zalo.service';
import { ZaloOaService } from './zalo-oa.service';
import { ZaloZnsService } from './zalo-zns.service';
import { ZaloWebhookService } from './zalo-webhook.service';
import { ZaloAgentService } from './zalo-agent.service';
import { ZaloSocialService } from './zalo-social.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000, // 30 seconds
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  providers: [
    ZaloService,
    ZaloOaService,
    ZaloZnsService,
    ZaloWebhookService,
    ZaloAgentService,
    ZaloSocialService,
  ],
  exports: [
    ZaloService,
    ZaloOaService,
    ZaloZnsService,
    ZaloWebhookService,
    ZaloAgentService,
    ZaloSocialService,
  ],
})
export class ZaloModule {}
