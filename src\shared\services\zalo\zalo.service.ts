import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosError, AxiosRequestConfig } from 'axios';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloAccessToken, ZaloResponse, ZaloRefreshTokenResponse, ZaloSocialUserInfo } from './zalo.interface';

@Injectable()
export class ZaloService {
  private readonly logger = new Logger(ZaloService.name);
  private readonly apiUrl = 'https://openapi.zalo.me';
  private readonly oaApiUrl = 'https://openapi.zalo.me/v3.0/oa';
  private readonly socialApiUrl = 'https://graph.zalo.me';

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Lấy access token cho Official Account
   * @param appId ID của ứng dụng
   * @param appSecret Secret của ứng dụng
   * @param code Authorization code
   * @param redirectUri URI chuyển hướng
   * @returns Access token
   * @throws AppException nếu có lỗi xảy ra
   */
  async getOaAccessToken(
    appId: string,
    appSecret: string,
    code: string,
    redirectUri: string,
  ): Promise<ZaloAccessToken> {
    try {
      const url = `${this.oaApiUrl}/oauth/access_token`;
      const params = {
        app_id: appId,
        app_secret: appSecret,
        code,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
      };

      const response = await lastValueFrom(
        this.httpService.post<ZaloResponse<ZaloAccessToken>>(url, null, { params }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy access token: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy access token: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      // This will always throw an exception, so the function will never return undefined
      throw this.handleError(error, 'Lỗi khi lấy access token');
    }
  }

  /**
   * Làm mới access token cho Official Account
   * @param appId ID của ứng dụng
   * @param appSecret Secret của ứng dụng
   * @param refreshToken Refresh token
   * @returns Access token mới
   * @throws AppException nếu có lỗi xảy ra
   */
  async refreshOaAccessToken(
    appId: string,
    appSecret: string,
    refreshToken: string,
  ): Promise<ZaloAccessToken> {
    try {
      const url = `${this.oaApiUrl}/oauth/access_token`;
      const params = {
        app_id: appId,
        app_secret: appSecret,
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      };

      const response = await lastValueFrom(
        this.httpService.post<ZaloResponse<ZaloAccessToken>>(url, null, { params }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi làm mới access token: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi làm mới access token: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      // This will always throw an exception, so the function will never return undefined
      throw this.handleError(error, 'Lỗi khi làm mới access token');
    }
  }

  /**
   * Lấy access token cho Social API
   * @param appId ID của ứng dụng
   * @param appSecret Secret của ứng dụng
   * @param code Authorization code
   * @param redirectUri URI chuyển hướng
   * @returns Access token
   * @throws AppException nếu có lỗi xảy ra
   */
  async getSocialAccessToken(
    appId: string,
    appSecret: string,
    code: string,
    redirectUri: string,
  ): Promise<ZaloAccessToken> {
    try {
      const url = `${this.socialApiUrl}/oauth/access_token`;
      const params = {
        app_id: appId,
        app_secret: appSecret,
        code,
        redirect_uri: redirectUri,
      };

      const response = await lastValueFrom(
        this.httpService.get<ZaloResponse<ZaloAccessToken>>(url, { params }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy social access token: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy social access token: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      // This will always throw an exception, so the function will never return undefined
      throw this.handleError(error, 'Lỗi khi lấy social access token');
    }
  }

  /**
   * Tạo URL xác thực cho Official Account
   * @param appId ID của ứng dụng
   * @param redirectUri URI chuyển hướng
   * @returns URL xác thực
   */
  createOaAuthUrl(appId: string, redirectUri: string): string {
    const baseUrl = 'https://oauth.zaloapp.com/v4/oa/permission';
    const params = new URLSearchParams({
      app_id: appId,
      redirect_uri: redirectUri,
      state: 'oa_auth',
    });

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Tạo URL xác thực cho Social API
   * @param appId ID của ứng dụng
   * @param redirectUri URI chuyển hướng
   * @returns URL xác thực
   */
  createSocialAuthUrl(appId: string, redirectUri: string): string {
    const baseUrl = 'https://oauth.zaloapp.com/v4/permission';
    const params = new URLSearchParams({
      app_id: appId,
      redirect_uri: redirectUri,
      state: 'social_auth',
    });

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Làm mới access token từ refresh token cho Social API
   * @param appId ID của ứng dụng
   * @param refreshToken Refresh token
   * @returns Access token mới
   * @throws AppException nếu có lỗi xảy ra
   */
  async refreshSocialAccessToken(
    appId: string,
    refreshToken: string,
  ): Promise<ZaloRefreshTokenResponse> {
    try {
      const url = `${this.socialApiUrl}/oauth/access_token`;
      const params = {
        app_id: appId,
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<ZaloResponse<ZaloRefreshTokenResponse>>(url, { params }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi làm mới social access token: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi làm mới social access token: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      throw this.handleError(error, 'Lỗi khi làm mới social access token');
    }
  }

  /**
   * Lấy thông tin người dùng từ Social API
   * @param accessToken Access token của người dùng
   * @param fields Các trường thông tin cần lấy (mặc định: id,name,picture)
   * @returns Thông tin người dùng
   * @throws AppException nếu có lỗi xảy ra
   */
  async getSocialUserInfo(
    accessToken: string,
    fields: string = 'id,name,picture',
  ): Promise<ZaloSocialUserInfo> {
    try {
      const url = `${this.socialApiUrl}/me`;
      const params = {
        access_token: accessToken,
        fields,
      };

      const response = await lastValueFrom(
        this.httpService.get<ZaloSocialUserInfo>(url, { params }),
      );

      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Lỗi khi lấy thông tin người dùng từ Social API');
    }
  }

  /**
   * Kiểm tra tính hợp lệ của access token
   * @param accessToken Access token cần kiểm tra
   * @returns True nếu token hợp lệ, false nếu không
   */
  async validateAccessToken(accessToken: string): Promise<boolean> {
    try {
      await this.getSocialUserInfo(accessToken, 'id');
      return true;
    } catch (error) {
      this.logger.warn(`Access token không hợp lệ: ${error.message}`);
      return false;
    }
  }

  /**
   * Gửi yêu cầu GET đến Zalo API
   * @param url URL của API
   * @param accessToken Access token
   * @param params Tham số truy vấn
   * @returns Dữ liệu phản hồi
   * @throws AppException nếu có lỗi xảy ra
   */
  async get<T>(url: string, accessToken: string, params?: Record<string, any>): Promise<T> {
    try {
      const config: AxiosRequestConfig = {
        headers: {
          'access_token': accessToken,
        },
        params,
      };

      const response = await lastValueFrom(
        this.httpService.get<ZaloResponse<T>>(url, config),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gọi API Zalo: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gọi API Zalo GET: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      // This will always throw an exception, so the function will never return undefined
      throw this.handleError(error, 'Lỗi khi gọi API Zalo GET');
    }
  }

  /**
   * Gửi yêu cầu POST đến Zalo API
   * @param url URL của API
   * @param accessToken Access token
   * @param data Dữ liệu gửi đi
   * @param params Tham số truy vấn
   * @returns Dữ liệu phản hồi
   * @throws AppException nếu có lỗi xảy ra
   */
  async post<T>(
    url: string,
    accessToken: string,
    data?: any,
    params?: Record<string, any>,
  ): Promise<T> {
    try {
      const config: AxiosRequestConfig = {
        headers: {
          'access_token': accessToken,
          'Content-Type': 'application/json',
        },
        params,
      };

      const response = await lastValueFrom(
        this.httpService.post<ZaloResponse<T>>(url, data, config),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gọi API Zalo: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gọi API Zalo POST: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      // This will always throw an exception, so the function will never return undefined
      throw this.handleError(error, 'Lỗi khi gọi API Zalo POST');
    }
  }

  /**
   * Xử lý lỗi từ Zalo API và luôn ném ra một exception
   * @param error Lỗi
   * @param defaultMessage Thông báo mặc định
   * @throws AppException
   */
  private handleError(error: any, defaultMessage: string): never {
    this.logger.error(`${defaultMessage}: ${error.message}`, error.stack);

    if (error instanceof AppException) {
      throw error;
    }

    if (error instanceof AxiosError && error.response) {
      const response = error.response.data as ZaloResponse<any>;
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `${defaultMessage}: ${response.message || error.message}`,
      );
    }

    throw new AppException(
      ErrorCode.EXTERNAL_SERVICE_ERROR,
      `${defaultMessage}: ${error.message}`,
    );
  }
}
