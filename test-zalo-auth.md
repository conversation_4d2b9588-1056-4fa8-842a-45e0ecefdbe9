# Test Zalo OAuth API

## Cấu hình Environment Variables

Thêm vào file `.env`:

```env
ZALO_APP_ID=785984350482246426
ZALO_APP_SECRET=4wClko3npQPavdlJHk76
ZALO_REDIRECT_URI=https://your-domain.com/auth/zalo/callback
```

## API Endpoints

### 1. Lấy URL xác thực <PERSON>alo

**GET** `/auth/zalo/auth-url`

**Query Parameters:**
- `redirectUri` (optional): URL chuyển hướng tùy chỉnh

**Response:**
```json
{
  "code": 200,
  "message": "Lấy URL xác thực Zalo thành công",
  "result": {
    "url": "https://oauth.zaloapp.com/v4/permission?app_id=785984350482246426&redirect_uri=https%3A%2F%2Fyour-domain.com%2Fauth%2Fzalo%2Fcallback&scope=id%2Cname%2Cpicture"
  }
}
```

### 2. Đăng nhập bằng Zalo

**POST** `/auth/zalo/login`

**Request Body:**
```json
{
  "code": "authorization_code_from_zalo",
  "redirectUri": "https://your-domain.com/auth/zalo/callback",
  "ref": 12345
}
```

**Response:**
```json
{
  "code": 200,
  "message": "Đăng nhập Zalo thành công",
  "result": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 86400,
    "expiresAt": 1746968772000,
    "info": [],
    "user": {
      "id": 1,
      "email": "",
      "username": "",
      "permissions": ["read:profile", "write:profile"],
      "status": "active"
    }
  }
}
```

## Flow đăng nhập Zalo

1. **Frontend gọi API lấy URL xác thực:**
   ```javascript
   const response = await fetch('/auth/zalo/auth-url');
   const { result } = await response.json();
   window.location.href = result.url;
   ```

2. **Người dùng được chuyển hướng đến Zalo để xác thực**

3. **Zalo chuyển hướng về với authorization code:**
   ```
   https://your-domain.com/auth/zalo/callback?code=AUTHORIZATION_CODE&state=STATE
   ```

4. **Frontend gọi API đăng nhập với code:**
   ```javascript
   const response = await fetch('/auth/zalo/login', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json'
     },
     body: JSON.stringify({
       code: 'AUTHORIZATION_CODE',
       redirectUri: 'https://your-domain.com/auth/zalo/callback'
     })
   });
   ```

## Xử lý tài khoản

- **Nếu chưa có tài khoản:** Tự động tạo tài khoản mới với thông tin từ Zalo
- **Nếu đã có tài khoản:** Đăng nhập bình thường
- **Avatar:** Tự động lưu URL avatar từ Zalo vào trường `avatar` của user
- **Email:** Zalo không cung cấp email, nên `isVerifyEmail` sẽ là `false`

## Database Changes

Đã thêm trường `zaloId` vào bảng `users`:

```sql
ALTER TABLE users ADD COLUMN zalo_id VARCHAR(255) NULL UNIQUE COMMENT 'Zalo ID';
```
